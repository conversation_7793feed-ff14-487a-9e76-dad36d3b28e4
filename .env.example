﻿# LANStreamer Environment Configuration
# Copy this file to .env and modify as needed

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# Security (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=CHANGE_THIS_VALUE_lanstreamer-dev-secret-key
JWT_EXPIRES_IN=24h
ADMIN_PASSWORD=CHANGE_THIS_VALUE_admin123
SESSION_SECRET=CHANGE_THIS_VALUE_session-secret-change-this

# Icecast Configuration (CHANGE PASSWORDS!)
ICECAST_HOST=localhost
ICECAST_PORT=8000
ICECAST_ADMIN_PASSWORD=CHANGE_THIS_VALUE_hackme
ICECAST_SOURCE_PASSWORD=CHANGE_THIS_VALUE_hackme
ICECAST_CONFIG_PATH=./config/icecast.xml

# FFmpeg Configuration
FFMPEG_PATH=ffmpeg
FFMPEG_LOG_LEVEL=info
MAX_CONCURRENT_STREAMS=16

# Audio Configuration
DEFAULT_BITRATE=128k
DEFAULT_SAMPLE_RATE=44100
DEFAULT_CHANNELS=2
AUDIO_BUFFER_SIZE=1024

# Network Configuration
STREAM_BASE_URL=http://localhost:8000
CLIENT_PORT=8080
WEBSOCKET_PORT=3001

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Development
ENABLE_CORS=true
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Hardware Simulation (for development)
SIMULATE_HARDWARE=true
VIRTUAL_AUDIO_DEVICE=VB-Cable

# External Integrations
OBS_STUDIO_PATH=
ENABLE_OBS_INTEGRATION=true

# Data Storage (file-based)
CONFIG_DB_PATH=./data/config.json
STREAMS_DB_PATH=./data/streams.json

# Monitoring
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# Security Headers
ENABLE_HELMET=true
ENABLE_COMPRESSION=true

# File Upload
MAX_FILE_SIZE=10mb
UPLOAD_PATH=./uploads

# Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600000
BACKUP_PATH=./backups
