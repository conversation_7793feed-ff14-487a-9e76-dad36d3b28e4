# LANStreamer Installation Guides

This directory contains comprehensive installation and setup guides for LANStreamer dependencies.

## Available Guides

- **[FFmpeg Installation Guide](./ffmpeg-installation.md)** - Complete guide for installing and updating FFmpeg on all platforms
- **[Icecast Installation Guide](./icecast-installation.md)** - Complete guide for installing and updating Icecast server on all platforms

## Quick Links

| Component | Status Check | Installation Guide |
|-----------|-------------|-------------------|
| FFmpeg | Run `ffmpeg -version` in terminal | [FFmpeg Guide](./ffmpeg-installation.md) |
| Icecast | Check if service is running | [Icecast Guide](./icecast-installation.md) |

## Supported Platforms

- ✅ **Windows** (10/11)
- ✅ **macOS** (Intel & Apple Silicon)
- ✅ **Linux** (Ubuntu/Debian/CentOS/RHEL)

## Getting Help

If you encounter issues with installation:

1. Check the troubleshooting sections in each guide
2. Review the [LANStreamer Technical Specification](../LANStreamer-Technical-Specification.md)
3. Open an issue on our [GitHub repository](https://github.com/jerryagenyi/LANStreamer/issues){:target="_blank"}

## Integration with LANStreamer

These guides are referenced by LANStreamer's status indicators:
- Dashboard tooltips link to these guides when components are not installed
- Status checks use the verification commands documented here
