[33m24a2ff2[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m)[m debug: Add cache-busting and component debug tools
[33m9a9c340[m enhance: Major UI improvements for LobbyMusicPlayer component
[33maec397e[m fix: Add LobbyMusicPlayer to window object for ComponentManager detection - CRITICAL FIX: Added window.LobbyMusicPlayer to make class globally accessible
[33m75e87ab[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m debug: Fix ComponentManager initialization timing and add debug tools
[33m17165dd[m feat: Add component status indicators to all dashboard sections
[33m761858d[m feat: Implement conditional component rendering with static fallbacks
[33m27bc85b[m feat: Create modular Lobby Background Music Player component
[33m02d764b[m feat: Update dashboard to match stitch-index-3 design with functional logo and API integration
[33mda51569[m feat: Update UI to exactly match stitch-index-2.html design
[33m9239323[m feat: Major dashboard enhancement - Tailwind CSS restoration with smart status indicators
