﻿ffmpeg : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
At line:1 char:1
+ ffmpeg -list_devices true -f dshow -i dummy 2>&1 | Out-File -FilePath ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (ffmpeg version ...mpeg developers:String) [], RemoteExcep 
   tion
    + FullyQualifiedErrorId : NativeCommandError
 
  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads 
--disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 
--enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib 
--enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray 
--enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 
--enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc 
--enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e 
--enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 
--enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx 
--enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi 
--enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg 
--enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va 
--enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc 
--enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme 
--enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame 
--enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 
--enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus 
--enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite 
--enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
[dshow @ 0000026ee37d6700] "Logitech HD Pro Webcam C910" (video)
[dshow @ 0000026ee37d6700]   Alternative name "@device_pnp_\\?\usb#vid_046d&pid_0821&mi_02#7&10249771&1
&0002#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\{bbefb6c7-2fc4-4139-bb8b-a58bba724083}"
[dshow @ 0000026ee37d6700] "Galaxy A52s 5G (Windows Virtual Camera)" (video)
[dshow @ 0000026ee37d6700]   Alternative name "@device_pnp_\\?\swd#vcamdevapi#08e9946ca1c56f574e8b6b7cc
b62b046c58df634aa556740dc72e4e65b752855#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\{fcebba03-9d13-4c13-9940
-cc84fcd132d1}"
[dshow @ 0000026ee37d6700] "Meta Quest 3" (video)
[dshow @ 0000026ee37d6700]   Alternative name 
"@device_sw_{860BB310-5D01-11D0-BD3B-00A0C911CE86}\{0FEDCBA9-8765-4321-0FED-CBA987654321}"
[dshow @ 0000026ee37d6700] "Meta Quest 3S" (video)
[dshow @ 0000026ee37d6700]   Alternative name 
"@device_sw_{860BB310-5D01-11D0-BD3B-00A0C911CE86}\{12345678-90AB-CDEF-1234-567890ABCDEF}"
