{"name": "lanst<PERSON>mer-frontend", "version": "1.0.0", "description": "LANStreamer Frontend - Vue.js application for audio streaming management", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --mode development", "preview": "vue-cli-service serve --mode production"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "axios": "^1.4.0", "socket.io-client": "^4.7.2", "@fortawesome/fontawesome-free": "^6.4.0", "bootstrap": "^5.3.0", "bootstrap-vue-next": "^0.8.9", "chart.js": "^4.3.0", "vue-chartjs": "^5.2.0", "qrcode": "^1.5.3", "moment": "^2.29.4", "lodash": "^4.17.21", "vue-toastification": "^2.0.0-rc.5", "vue3-perfect-scrollbar": "^1.6.1"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^8.0.1", "eslint": "^8.44.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.15.1", "sass": "^1.63.6", "sass-loader": "^13.3.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}