/* LANStreamer Main Styles */

/* CSS Variables */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  
  --border-radius: 6px;
  --border-radius-lg: 8px;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);
  
  --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-base: 14px;
  --line-height-base: 1.5;
  
  --transition-base: all 0.2s ease;
}

/* Base Styles */
body {
  font-family: var(--font-family-sans-serif);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: #212529;
  background-color: #f8f9fa;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

.text-muted {
  color: #6c757d !important;
}

.text-small {
  font-size: 12px;
}

.text-large {
  font-size: 16px;
}

/* Layout Utilities */
.container-fluid {
  padding-left: 15px;
  padding-right: 15px;
}

.section-header {
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #dee2e6;
}

.section-header h1,
.section-header h2 {
  margin-bottom: 0.25rem;
}

.section-header .subtitle {
  color: #6c757d;
  font-size: 14px;
  margin: 0;
}

/* Cards */
.card {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--box-shadow-lg);
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  padding: 1rem 1.25rem;
}

.card-body {
  padding: 1.25rem;
}

.card-footer {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 0.75rem 1.25rem;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition-base);
  border: 1px solid transparent;
}

.btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 12px;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 16px;
}

/* Form Controls */
.form-control {
  border-radius: var(--border-radius);
  border: 1px solid #ced4da;
  transition: var(--transition-base);
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-text {
  font-size: 12px;
  color: #6c757d;
}

/* Tables */
.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background: #f8f9fa;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

/* Badges */
.badge {
  font-weight: 500;
  border-radius: var(--border-radius);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.status-running {
  background: #d4edda;
  color: #155724;
}

.status-indicator.status-stopped {
  background: #f8d7da;
  color: #721c24;
}

.status-indicator.status-warning {
  background: #fff3cd;
  color: #856404;
}

.status-indicator.status-error {
  background: #f8d7da;
  color: #721c24;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.running {
  background: #28a745;
}

.status-dot.stopped {
  background: #dc3545;
}

.status-dot.warning {
  background: #ffc107;
}

.status-dot.error {
  background: #dc3545;
}

/* Progress Bars */
.progress {
  height: 8px;
  border-radius: 4px;
  background: #e9ecef;
}

.progress-bar {
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Alerts */
.alert {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
}

/* Navigation */
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: 700;
  font-size: 18px;
}

/* Sidebar */
.sidebar {
  background: #fff;
  border-right: 1px solid #dee2e6;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav-item {
  border-bottom: 1px solid #f8f9fa;
}

.sidebar-nav-link {
  display: block;
  padding: 0.75rem 1rem;
  color: #495057;
  text-decoration: none;
  transition: var(--transition-base);
}

.sidebar-nav-link:hover,
.sidebar-nav-link.active {
  background: #f8f9fa;
  color: var(--primary-color);
}

/* Utilities */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.border-radius {
  border-radius: var(--border-radius) !important;
}

.border-radius-lg {
  border-radius: var(--border-radius-lg) !important;
}

.box-shadow {
  box-shadow: var(--box-shadow) !important;
}

.box-shadow-lg {
  box-shadow: var(--box-shadow-lg) !important;
}

/* Animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 10px;
    padding-right: 10px;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .btn {
    font-size: 14px;
    padding: 8px 12px;
  }
  
  .table-responsive {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .section-header {
    margin-bottom: 1rem;
  }
  
  .card {
    margin-bottom: 15px;
  }
  
  .btn-group-vertical .btn {
    margin-bottom: 5px;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #333333;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
  
  .btn {
    display: none;
  }
}
