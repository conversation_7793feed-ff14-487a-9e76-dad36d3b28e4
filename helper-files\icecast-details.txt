# ===================================
# ICECAST STREAMING SERVER
# ===================================

# Icecast server hostname/IP
ICECAST_HOST=localhost

# Icecast server port (default: 8000)
ICECAST_PORT=8000

# Icecast admin password (CRITICAL: Change in production!)
ICECAST_ADMIN_PASSWORD=hackme

# Icecast source password for stream connections (CRITICAL: Change in production!)
ICECAST_SOURCE_PASSWORD=hackme

# ===================================
# ICECAST FILE PATHS (Manual Configuration)
# ===================================

# Path to Icecast executable
ICECAST_EXE_PATH="C:\Program Files (x86)\Icecast\bin\icecast.exe"

# Path to Icecast configuration file
ICECAST_CONFIG_PATH="C:\Program Files (x86)\Icecast\icecast.xml"

# Path to Icecast installation root (working directory)
ICECAST_ROOT_PATH="C:\Program Files (x86)\Icecast"

# Path to Icecast logs directory
ICECAST_LOGS_DIR="C:\Program Files (x86)\Icecast\logs"

# Path to Icecast web directory
ICECAST_WEB_DIR="C:\Program Files (x86)\Icecast\web"

# Path to Icecast admin directory
ICECAST_ADMIN_DIR="C:\Program Files (x86)\Icecast\admin"

# Path to Icecast batch file
ICECAST_BATCH_PATH="C:\Program Files (x86)\Icecast\icecast.bat"

# Remove these - let Icecast use its default relative paths
# ICECAST_ACCESS_LOG=""C:\Program Files (x86)\Icecast\log\access.log""
# ICECAST_ERROR_LOG=""C:\Program Files (x86)\Icecast\log\error.log""

