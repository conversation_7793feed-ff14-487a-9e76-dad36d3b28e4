# LANStreamer Images

This directory contains visual diagrams and illustrations for the LANStreamer documentation.

## Current Images

### 1. lanstreamer-local-audio-concept-1.jpg ✅ **ACTIVE**
**Description:** Main LANStreamer concept diagram showing local server approach
**Content:**
- 4 audio inputs (ENG, FRA, POR, ARA) with XLR connectors
- VLC cone icon representing "4 instances of local Audio streaming Generate servers links"
- Simple web interface box with "4 links, when clicked opens live audio listening"
- QR code for "Participants scan QR-code. Simple webpage, no downloads"
- Mobile phone showing language selection (ENG, FRA, POR, ITA)
- Text: "Instead of internet-based, we can setup a local server, and create an open-password SSID for participants to connect to"

### 2. lanstreamer-local-audio-concept-2.jpg ✅ **ACTIVE**
**Description:** Alternative streaming approach diagram
**Content:**
- Same 4 audio inputs (ENG, FRA, POR, ARA) with XLR connectors
- Cloud icon with "AUDIO STREAM" and "LIVE Audio, 4 channels streaming service"
- Same web interface and mobile phone elements
- Text: "Find me a simple, affordable local audio streaming app, maybe free, one-time pay or pay-per-hour (preferably)"

## Status

✅ **Both Images Active:** Displayed side-by-side in README concept section
✅ **Complete Visual:** Shows multiple implementation approaches
✅ **Ready to Display:** Both images show properly in GitHub README

## Image Guidelines

- **Format:** PNG preferred for diagrams
- **Size:** Optimize for web viewing (max 1200px width)
- **Quality:** High enough to read text clearly
- **Compression:** Balance quality vs file size for GitHub display
