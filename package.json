{"name": "lanstreamer-server", "version": "1.0.0", "description": "Backend server for the LANStreamer application.", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:simple": "nodemon src/server.js", "setup": "node scripts/setup.js", "test": "vitest run", "test:e2e": "playwright test"}, "keywords": ["streaming", "audio", "ffmpeg", "icecast"], "author": "", "license": "ISC", "dependencies": {"dotenv": "^17.2.1", "express": "^4.21.2", "fs-extra": "^11.3.1", "multer": "^2.0.2", "node-fetch": "^3.3.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@playwright/test": "^1.55.0", "browser-sync": "^3.0.4", "nodemon": "^2.0.0", "supertest": "^7.1.4", "vitest": "^3.2.4"}}