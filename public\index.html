<html><head>
    <meta charset="utf-8"/>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:FILL@1" rel="stylesheet"/>
    <title>LANStreamer</title>
    <link href="/assets/placeholder-logo.svg" rel="icon" type="image/svg+xml"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
          --primary-color: #00AEEF;
          --live-color: #4CAF50;
          --dark-bg: #111111;
          --card-bg: #1A1A1A;
          --border-color: #2F2F2F;
          --warning-color: #F85149;
        }
        .btn-gradient {
            background-image: linear-gradient(to right, #00AEEF, #0088CC);
            box-shadow: 0 4px 15px 0 rgba(0, 174, 239, 0.3);
        }
        .btn-gradient:hover {
            background-image: linear-gradient(to right, #0088CC, #00AEEF);
            box-shadow: 0 6px 20px 0 rgba(0, 174, 239, 0.4);
        }
        .btn-stop-gradient {
            background-image: linear-gradient(to right, #F85149, #D43F3A);
            box-shadow: 0 4px 15px 0 rgba(248, 81, 73, 0.3);
        }
        .btn-stop-gradient:hover {
            background-image: linear-gradient(to right, #D43F3A, #F85149);
            box-shadow: 0 6px 20px 0 rgba(248, 81, 73, 0.4);
        }
        .pulse-live {
            animation: pulse-live 2s infinite;
        }
        @keyframes pulse-live {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
            }
        }
        input[type=range] {
            -webkit-appearance: none;
            appearance: none;
            background: transparent;
            cursor: pointer;
            width: 100%;
        }
        input[type=range]:focus {
            outline: none;
        }
        input[type=range]::-webkit-slider-runnable-track {
            background-color: #2F2F2F;
            border-radius: 0.5rem;
            height: 0.5rem;
        }
        input[type=range]::-webkit-slider-thumb {
            -webkit-appearance: none;appearance: none;
            margin-top: -4px;background-color: var(--primary-color);
            border-radius: 50%;
            height: 1rem;
            width: 1rem;
            box-shadow: 0 0 5px rgba(0, 174, 239, 0.5);
         }
        input[type=range]::-moz-range-track {
            background-color: #2F2F2F;
            border-radius: 0.5rem;
            height: 0.5rem;
        }
        input[type=range]::-moz-range-thumb {
            background-color: var(--primary-color);
            border: none;border-radius: 50%;
            height: 1rem;
            width: 1rem;
            box-shadow: 0 0 5px rgba(0, 174, 239, 0.5);
        }
      </style>
    </head>
    <body class="bg-[var(--dark-bg)]">
    <div class="relative flex size-full min-h-screen flex-col bg-gradient-to-br from-[#111111] to-[#0A0A0A] dark group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
    <header class="sticky top-0 z-50 flex items-center justify-between whitespace-nowrap border-b border-solid border-[var(--border-color)] bg-[var(--dark-bg)]/80 px-10 py-4 backdrop-blur-lg">
    <div class="flex items-center gap-4 text-white">
        <a href="http://localhost:3001" class="flex items-center gap-2 text-white hover:text-[var(--primary-color)] transition-colors">
            <img src="/assets/lanstreamer-logo.png" alt="LANStreamer" class="h-16 w-auto" id="logo-image" onerror="this.style.display='none'; document.getElementById('logo-fallback').style.display='flex';">
            <div id="logo-fallback" style="display: none;" class="flex items-center">
                <span class="text-xl font-bold tracking-wider">LANStreamer</span>
            </div>
        </a>
    </div>
    <div class="flex items-center gap-3">
    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 ml-4" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDSIzxTEv03JUEwpU1vqD79kbeH63SzCaGfIIFbumOYFeslE7o8s5BELsy84X5hdHbqgGM91dgl8SECCQAuQUyN5TzULy0C6hTXlMd2FdmoEp4c0WAiQhyasooFF0UAFZoOLRnhrfiUJjHhWPasQr2g2MWISZolmDkAER-lahc8ZqnqZSr95hHY9Z-0DIFIJDnrExTC0CHjbWsTgsGY2Pj1xH87e8JybvN0dCpF-U1oWs_K8Q3OXkC3yMhMSgHVXCilnq_8LYez-3g");'></div>
    </div>
    </header>
    <main class="flex-1 px-4 sm:px-6 lg:px-10 py-8">
    <div class="mx-auto max-w-7xl">
    <div class="mb-8 p-4 rounded-xl bg-yellow-500/10 border border-yellow-500/50 text-yellow-400">
    <div class="flex items-center gap-3">
    <span class="material-symbols-rounded text-2xl"> warning </span>
    <p class="font-medium">
    <strong>Security Warning:</strong> Your LANStreamer instance is running in development mode. For production use, please configure SSL and authentication.
                  </p>
    </div>
    </div>
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Left Column (Primary Controls) -->
        <div class="xl:col-span-2 space-y-8">
            
            <!-- FFmpeg Streams - Component or Static -->
            <div id="ffmpeg-streams"></div>
            
            <!-- Audio Devices - Component or Static -->
            <div id="audio-devices"></div>
            
            <!-- Video Sources - Collapsible Panel (Hidden by default) -->
            <div class="collapsible-section">
                <button id="video-sources-toggle" class="w-full flex items-center justify-between p-3 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-xl hover:bg-[var(--card-bg)]/80 transition-all duration-300">
                    <div class="flex items-center gap-3">
                        <span class="material-symbols-rounded text-gray-400">videocam</span>
                        <span class="text-gray-300 font-medium">Video Sources</span>
                        <span class="text-xs text-gray-500">(Future Feature)</span>
                    </div>
                    <span class="material-symbols-rounded text-gray-400 transition-transform duration-300" id="video-sources-arrow">expand_more</span>
                </button>
                <div id="video-sources-content" class="hidden mt-2">
                    <div id="video-sources"></div>
                </div>
            </div>
        </div>
        
        <!-- Right Column (Sidebar) -->
        <div class="space-y-8">
            
            <!-- Stream Controls - Component or Static -->
            <div id="stream-controls"></div>
            
                    <!-- Icecast Server - Component or Static -->
        <div id="icecast-server">
            <!-- IcecastManager component will be initialized here -->
        </div>
            
            <!-- Lobby Background Music - Collapsible Panel (Hidden by default) -->
            <div class="collapsible-section">
                <button id="lobby-music-toggle" class="w-full flex items-center justify-between p-3 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-xl hover:bg-[var(--card-bg)]/80 transition-all duration-300">
                    <div class="flex items-center gap-3">
                        <span class="material-symbols-rounded text-gray-400">music_note</span>
                        <span class="text-gray-300 font-medium">Lobby Background Music</span>
                        <span class="text-xs text-gray-500">(Listening Only - Not for Streaming)</span>
                    </div>
                    <span class="material-symbols-rounded text-gray-400 transition-transform duration-300" id="lobby-music-arrow">expand_more</span>
                </button>
                <div id="lobby-music-content" class="hidden mt-2">
                    <div id="lobby-music-player"></div>
                </div>
            </div>
        </div>
    </div>

        <!-- Static Content Templates (Hidden) -->

        <!-- FFmpeg Streams Static Template - DISABLED FOR DEBUGGING -->
        <div id="ffmpeg-streams-static" style="display: none !important;">
            <!-- Static content disabled - should use dynamic FFmpegStreamsManager -->
        </div>

        <!-- Audio Devices Static Template (Hidden - Dynamic content will be rendered) -->
        <div id="audio-devices-static" style="display: none;">
            <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-white">Audio Devices captured by FFmpeg</h2>
                    <button class="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-semibold text-white bg-[var(--primary-color)]/10 hover:bg-[var(--primary-color)]/20 border border-[var(--border-color)] transition-all duration-300">
                        <span class="material-symbols-rounded text-base">refresh</span>
                        Refresh device list
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-3">Audio Input Devices</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-[#111111] rounded-xl border border-[var(--border-color)]">
                                <div class="flex items-center gap-3">
                                    <span class="material-symbols-rounded text-2xl text-[var(--live-color)]">mic</span>
                                    <div>
                                        <p class="font-semibold text-white text-sm">Focusrite Scarlett 2i2</p>
                                        <p class="text-xs text-gray-400">USB Audio Device</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-[#111111] rounded-xl border border-[var(--border-color)]">
                                <div class="flex items-center gap-3">
                                    <span class="material-symbols-rounded text-2xl text-gray-600">mic_off</span>
                                    <div>
                                        <p class="font-semibold text-white text-sm">Default Microphone</p>
                                        <p class="text-xs text-gray-400">System Default</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-3">Audio Output Devices</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-[#111111] rounded-xl border border-[var(--border-color)]">
                                <div class="flex items-center gap-3">
                                    <span class="material-symbols-rounded text-2xl text-gray-600">headphones</span>
                                    <div>
                                        <p class="font-semibold text-white text-sm">Realtek Digital Output</p>
                                        <p class="text-xs text-gray-400">Onboard Audio</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-[#111111] rounded-xl border border-[var(--border-color)]">
                                <div class="flex items-center gap-3">
                                    <span class="material-symbols-rounded text-2xl text-gray-600">speaker</span>
                                    <div>
                                        <p class="font-semibold text-white text-sm">Monitor Speakers</p>
                                        <p class="text-xs text-gray-400">NVIDIA High Definition Audio</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Sources Static Template -->
        <div id="video-sources-static" style="display: none;">
            <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-white">Video Sources captured by FFmpeg</h2>
                    <button class="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-semibold text-white bg-[var(--primary-color)]/10 hover:bg-[var(--primary-color)]/20 border border-[var(--border-color)] transition-all duration-300">
                        <span class="material-symbols-rounded text-base">refresh</span>
                        Refresh device list
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-[#111111] rounded-xl border border-[var(--border-color)]">
                        <div class="flex items-center gap-3">
                            <span class="material-symbols-rounded text-2xl text-gray-600">videocam_off</span>
                            <div>
                                <p class="font-semibold text-white">No video sources detected</p>
                                <p class="text-sm text-gray-400">Connect a camera or capture card.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stream Controls Static Template -->
        <div id="stream-controls-static" style="display: none;">
            <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-white">Stream Controls</h2>
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-mono text-gray-400 bg-[#111111] border border-[var(--border-color)] px-2 py-0.5 rounded-md">2/3 running</span>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row gap-4">
                    <button class="w-full inline-flex items-center justify-center gap-2 rounded-md px-4 py-3 text-sm font-semibold text-white shadow-lg transition-all duration-300 btn-gradient">
                        <span class="material-symbols-rounded">play_arrow</span>
                        Start All Streams
                    </button>
                    <button class="w-full inline-flex items-center justify-center gap-2 rounded-md px-4 py-3 text-sm font-semibold text-white shadow-lg transition-all duration-300 btn-stop-gradient">
                        <span class="material-symbols-rounded">stop</span>
                        End All Streams
                    </button>
                </div>
            </div>
        </div>

        <!-- Icecast Server Static Template -->
        <div id="icecast-server-static" style="display: none;">
            <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-xl font-bold text-white">Icecast Server</h2>
                    <div class="flex items-center gap-3">
                        <div class="flex items-center gap-2">
                            <span class="h-3 w-3 rounded-full bg-red-600"></span>
                            <span class="text-sm font-medium text-gray-300">Not Installed - <a href="https://github.com/jerryagenyi/LANStreamer/blob/main/docs/guides/icecast-installation.md" target="_blank" class="text-blue-400 hover:text-blue-300 underline">setup guide</a></span>
                        </div>
                        <button class="inline-flex items-center justify-center gap-2 rounded-md px-2 py-1 text-xs font-medium text-white bg-[var(--card-bg)] border border-[var(--border-color)] hover:bg-[var(--border-color)] transition-all duration-300" title="Check for installation">
                            <span class="material-symbols-rounded text-sm">refresh</span>
                        </button>
                    </div>
                </div>
                <div class="space-y-5 opacity-50">
                    <!-- Component Notification -->
                    <div class="flex items-center gap-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
                        <span class="material-symbols-rounded text-yellow-500">code_off</span>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-yellow-400">Using Static Content - Component Not Available</p>
                        </div>
                        <span class="text-xs text-yellow-500 font-medium">Icecast Server</span>
                    </div>
                    
                    <!-- Server Status -->
                    <div class="flex items-center gap-4 p-3 bg-[#111111] border border-[var(--border-color)] rounded-xl">
                        <span class="material-symbols-rounded text-2xl text-red-500">code_off</span>
                        <div class="flex-1">
                            <p class="font-semibold text-white">Icecast Server Component Not Available</p>
                            <p class="text-sm text-gray-400">Using Static Content - Component Not Available</p>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex gap-2 pt-4 border-t border-[var(--border-color)]">
                        <button class="flex-1 inline-flex items-center justify-center gap-2 rounded-md px-3 py-2 text-sm font-semibold text-white shadow-lg transition-all duration-300 btn-gradient opacity-50 cursor-not-allowed" disabled>
                            <span class="material-symbols-rounded text-base">play_arrow</span>
                            Start
                        </button>
                        <button class="flex-1 inline-flex items-center justify-center gap-2 rounded-md px-3 py-2 text-sm font-semibold text-white shadow-lg transition-all duration-300 btn-stop-gradient opacity-50 cursor-not-allowed" disabled>
                            <span class="material-symbols-rounded text-base">stop</span>
                            Stop
                        </button>
                        <button class="flex-1 inline-flex items-center justify-center gap-2 rounded-md px-3 py-2 text-sm font-semibold text-white shadow-lg transition-all duration-300 bg-[var(--card-bg)] border border-[var(--border-color)] opacity-50 cursor-not-allowed" disabled>
                            <span class="material-symbols-rounded text-base">restart</span>
                            Restart
                        </button>
                    </div>
                    
                    <!-- Server Info -->
                    <div class="border-t border-[var(--border-color)] pt-4 mt-4 text-xs text-gray-500 space-y-1">
                        <p><strong>Host:</strong> -</p>
                        <p><strong>Port:</strong> -</p>
                        <p><strong>Uptime:</strong> -</p>
                        <p><strong>Listeners:</strong> 0 / 100</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lobby Music Player Static Template -->
        <div id="lobby-music-static" style="display: none;">
            <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                <h2 class="text-xl font-bold text-white mb-4">Lobby Background Music</h2>
                <div class="bg-[#111111] border border-[var(--border-color)] rounded-xl p-4 space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <div class="w-2 h-10 rounded-full bg-gray-600"></div>
                            <div>
                                <p class="font-semibold text-white">Background Music</p>
                                <p class="text-sm text-gray-500 font-medium flex items-center gap-1">
                                    <span class="material-symbols-rounded text-sm">circle</span> Stopped
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <button class="inline-flex items-center justify-center gap-2 rounded-md px-4 py-2 text-sm font-semibold text-white shadow-lg transition-all duration-300 btn-gradient">
                                <span class="material-symbols-rounded">play_arrow</span>
                                Start
                            </button>
                        </div>
                    </div>
                    <div class="pt-4 border-t border-gray-800 space-y-4">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-mono text-gray-400 truncate max-w-[200px] md:max-w-xs">Slack-Huddle-Hold-Music_Daniel-Simmons.mp3</p>
                            <button class="text-sm font-semibold text-[var(--primary-color)] hover:text-white transition-colors">Change</button>
                        </div>
                        <div class="flex items-center gap-4">
                            <button class="flex h-8 w-8 items-center justify-center rounded-md border border-[var(--border-color)] bg-[#2A2A2A] text-gray-400 hover:bg-[var(--primary-color)]/20 hover:text-[var(--primary-color)] transition-colors">
                                <span class="material-symbols-rounded text-base">volume_off</span>
                            </button>
                            <div class="flex-1">
                                <input class="w-full" max="100" min="0" type="range" value="75"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
    </main>
    </div>

    <!-- Include Components -->
    <script src="/components/ComponentManager.js"></script>
    <script src="/components/LobbyMusicPlayer.js"></script>
    <script src="/components/FileBrowser.js"></script>
    <script src="/components/IcecastManager.js"></script>
    <script src="/components/FFmpegStreamsManager.js"></script>

    <!-- Debug script loading -->
    <script>
        console.log('🔍 Script loading check:');
        console.log('- ComponentManager:', typeof ComponentManager);
        console.log('- FFmpegStreamsManager:', typeof FFmpegStreamsManager);
        console.log('- window.FFmpegStreamsManager:', typeof window.FFmpegStreamsManager);
        console.log('- ffmpeg-streams container:', document.getElementById('ffmpeg-streams'));

        // Additional debugging for FFmpeg streams
        setTimeout(() => {
            console.log('🔍 Post-load check:');
            console.log('- FFmpeg container exists:', !!document.getElementById('ffmpeg-streams'));
            console.log('- FFmpeg container content:', document.getElementById('ffmpeg-streams')?.innerHTML?.length || 0, 'chars');
            console.log('- Global ffmpegStreamsManager:', typeof window.ffmpegStreamsManager);
            console.log('- FFmpeg manager initialized:', window.ffmpegStreamsManager?.isInitialized);

            // Force render if component exists but container is empty
            const container = document.getElementById('ffmpeg-streams');
            if (container && container.innerHTML.trim().length === 0 && window.ffmpegStreamsManager) {
                console.log('🔧 Container empty, forcing FFmpeg component initialization...');
                window.ffmpegStreamsManager.init().catch(console.error);
            }
        }, 2000);
    </script>
    
    <script>
        class LANStreamerDashboard {
            constructor() {
                this.audioDevices = [];
                this.activeStreams = {};
                this.isLoading = false;
                this.icecastStatus = 'unknown';
                this.componentManager = null;
                this.lobbyMusicPlayer = null;
                this.init();
            }

            async init() {
                // Initialize ComponentManager after all scripts have loaded
                await this.initializeComponentManager();
                await this.loadAudioDevices();
                await this.checkFFmpegStatus();
                await this.initializeComponents();
                this.setupEventListeners();
                this.startStatusPolling();
                
                // Force render audio devices to ensure dynamic content is shown
                this.renderAudioDevices();
                
                // Icecast Manager will be initialized by ComponentManager with proper fallback
            }

            async initializeComponentManager() {
                // Ensure ComponentManager is available
                if (typeof ComponentManager === 'undefined') {
                    console.error('ComponentManager not loaded');
                    return;
                }

                this.componentManager = new ComponentManager();
                console.log('ComponentManager initialized');

                // Debug: Check what components are available
                console.log('Available components on window:', {
                    FFmpegStreamsManager: typeof window.FFmpegStreamsManager,
                    LobbyMusicPlayer: typeof window.LobbyMusicPlayer,
                    IcecastManager: typeof window.IcecastManager
                });
            }

            async initializeComponents() {
                if (!this.componentManager) {
                    console.error('ComponentManager not available for component initialization');
                    return;
                }

                // Debug: Check if LobbyMusicPlayer is available
                console.log('LobbyMusicPlayer available:', typeof LobbyMusicPlayer !== 'undefined');
                console.log('window.LobbyMusicPlayer:', typeof window.LobbyMusicPlayer);
                
                // Initialize all dashboard components with fallback to static
                await this.componentManager.initializeAll();
                
                // Log development status
                this.componentManager.logStatus();
                
                // Store reference to lobby music player for dashboard integration
                this.lobbyMusicPlayer = this.componentManager.getComponent('lobby-music-player');
                
                if (this.lobbyMusicPlayer) {
                    console.log('✅ Lobby Music Player component successfully integrated');
                } else {
                    console.log('⚠️ Lobby Music Player component not found - using static fallback');
                }
                
                // Store reference to icecast manager for dashboard integration
                this.icecastManager = this.componentManager.getComponent('icecast-server');
                
                if (this.icecastManager) {
                    console.log('✅ Icecast Manager component successfully integrated');
                } else {
                    console.log('⚠️ Icecast Manager component not found - using static fallback');
                }
                
                // FFmpeg Streams Manager will be initialized by ComponentManager
            }

            async loadAudioDevices(forceRefresh = false) {
                try {
                    const url = forceRefresh ? '/api/system/audio-devices/refresh' : '/api/system/audio-devices';
                    const method = forceRefresh ? 'POST' : 'GET';
                    
                    const response = await fetch(url, { method });
                    const data = await response.json();
                    
                    // Handle both old format (array) and new format (object with devices array)
                    this.audioDevices = data.devices || data;
                    
                    this.renderAudioDevices();
                    this.updateHeaderTooltip('audio-devices-status', `${this.audioDevices.length} Devices Online`);
                    
                    if (forceRefresh) {
                        console.log('Audio devices refreshed:', this.audioDevices);
                    }
                } catch (error) {
                    console.error('Failed to load audio devices:', error);
                    this.updateHeaderTooltip('audio-devices-status', 'Failed to load devices');
                    this.renderAudioDevicesError();
                }
            }

            renderAudioDevices() {
                const container = document.getElementById('audio-devices');
                if (!container) {
                    console.error('Audio devices container not found');
                    return;
                }

                if (!this.audioDevices || this.audioDevices.length === 0) {
                    container.innerHTML = `
                        <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-xl font-bold text-white">Audio Devices captured by FFmpeg</h2>
                                <button id="refresh-audio-devices" class="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-semibold text-white bg-[var(--primary-color)]/10 hover:bg-[var(--primary-color)]/20 border border-[var(--border-color)] transition-all duration-300">
                                    <span class="material-symbols-rounded text-base">refresh</span>
                                    Refresh device list
                                </button>
                            </div>
                            <div class="text-center py-8">
                                <span class="material-symbols-rounded text-4xl text-gray-600 mb-4">mic_off</span>
                                <p class="text-gray-400">No audio devices detected</p>
                            </div>
                        </div>
                    `;

                    // Add event listener for the refresh button
                    const refreshBtn = document.getElementById('refresh-audio-devices');
                    if (refreshBtn) {
                        refreshBtn.addEventListener('click', async () => {
                            await this.loadAudioDevices(true); // Force refresh
                            this.showNotification('Audio devices refreshed', 'info');
                        });
                    }

                    return;
                }

                // Separate input and output devices using deviceType property
                const inputDevices = this.audioDevices.filter(device =>
                    device.deviceType === 'input' ||
                    (!device.deviceType && (
                        device.name.toLowerCase().includes('mic') ||
                        device.name.toLowerCase().includes('input') ||
                        device.name.toLowerCase().includes('line in') ||
                        device.name.toLowerCase().includes('scarlett') ||
                        device.name.toLowerCase().includes('umc') ||
                        device.fallback // Include fallback devices as input
                    ))
                );

                const outputDevices = this.audioDevices.filter(device =>
                    device.deviceType === 'output' ||
                    (!device.deviceType && (
                        device.name.toLowerCase().includes('output') ||
                        device.name.toLowerCase().includes('speaker') ||
                        device.name.toLowerCase().includes('headphone') ||
                        device.name.toLowerCase().includes('monitor')
                    ))
                );

                // Use the properly separated devices
                const finalInputDevices = inputDevices;
                const finalOutputDevices = outputDevices;

                container.innerHTML = `
                    <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-bold text-white">🎤 Audio Input Sources for Streaming</h2>
                            <button id="refresh-audio-devices" class="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-semibold text-white bg-[var(--primary-color)]/10 hover:bg-[var(--primary-color)]/20 border border-[var(--border-color)] transition-all duration-300">
                                <span class="material-symbols-rounded text-base">refresh</span>
                                Refresh Devices
                            </button>
                        </div>

                        <div class="mb-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                            <div class="flex items-start gap-2">
                                <span class="material-symbols-rounded text-blue-400 text-lg mt-0.5">info</span>
                                <div class="text-sm">
                                    <p class="text-blue-300 font-medium">Streaming Focus</p>
                                    <p class="text-blue-200/80">These are audio input sources for streaming. Install <strong>VoiceMeeter</strong> or <strong>VB-Audio Virtual Cable</strong> to create virtual audio routing from your music players, DJ software, or other audio sources.</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            ${finalInputDevices.map(device => `
                                <div class="flex items-center justify-between p-4 bg-[#111111] rounded-xl border border-[var(--border-color)] hover:border-[var(--primary-color)]/30 transition-all duration-300">
                                    <div class="flex items-center gap-3">
                                        <span class="material-symbols-rounded text-2xl ${device.fallback ? (device.virtual ? 'text-purple-400' : 'text-gray-600') : 'text-[var(--live-color)]'}">
                                            ${device.virtual ? 'cable' : (device.fallback ? 'mic_off' : 'mic')}
                                        </span>
                                        <div>
                                            <p class="font-semibold text-white text-sm">${device.name}</p>
                                            <p class="text-xs text-gray-400">
                                                ${device.description || device.platform || 'System Device'}
                                                ${device.virtual ? ' • Virtual Audio' : ''}
                                                ${device.fallback ? ' • Fallback' : ''}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        ${device.virtual ? `
                                            <span class="px-2 py-1 text-xs font-medium bg-purple-500/20 text-purple-300 rounded-full">Virtual</span>
                                        ` : ''}
                                        ${device.fallback ? `
                                            <span class="px-2 py-1 text-xs font-medium bg-gray-500/20 text-gray-400 rounded-full">Fallback</span>
                                        ` : `
                                            <span class="px-2 py-1 text-xs font-medium bg-green-500/20 text-green-300 rounded-full">Available</span>
                                        `}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;

                // Add event listener for the refresh button
                const refreshBtn = document.getElementById('refresh-audio-devices');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', async () => {
                        await this.loadAudioDevices(true); // Force refresh
                        this.showNotification('Audio devices refreshed', 'info');
                    });
                }

                console.log('Audio devices rendered:', this.audioDevices);
            }

            renderAudioDevicesError() {
                const container = document.getElementById('audio-devices');
                if (!container) {
                    console.error('Audio devices container not found');
                    return;
                }

                container.innerHTML = `
                    <div class="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-2xl p-6 shadow-2xl shadow-black/30">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-bold text-white">Audio Devices captured by FFmpeg</h2>
                            <button id="refresh-audio-devices" class="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-semibold text-white bg-[var(--primary-color)]/10 hover:bg-[var(--primary-color)]/20 border border-[var(--border-color)] transition-all duration-300">
                                <span class="material-symbols-rounded text-base">refresh</span>
                                Refresh device list
                            </button>
                        </div>
                        <div class="text-center py-8">
                            <span class="material-symbols-rounded text-4xl text-red-500 mb-4">error</span>
                            <p class="text-red-400 mb-2">Failed to load audio devices</p>
                            <p class="text-gray-400 text-sm">Click refresh to try again</p>
                        </div>
                    </div>
                `;

                // Add event listener for the refresh button
                const refreshBtn = document.getElementById('refresh-audio-devices');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', async () => {
                        await this.loadAudioDevices(true); // Force refresh
                        this.showNotification('Audio devices refreshed', 'info');
                    });
                }

                console.error('Error rendering audio devices');
            }

            async checkIcecastStatus() {
                try {
                    const response = await fetch('/api/system/icecast-status');
                    const data = await response.json();
                    this.icecastStatus = data.running ? 'running' : 'stopped';
                    this.updateIcecastUI(data);
                    
                    if (data.running) {
                        this.updateHeaderTooltip('icecast-status-text', 'Server Online');
                    } else if (data.installed === false) {
                        this.updateHeaderTooltip('icecast-status-text', 
                            `Icecast not found. <a href="https://github.com/jerryagenyi/LANStreamer/blob/main/docs/guides/icecast-installation.md" target="_blank" class="text-blue-400 hover:text-blue-300 underline">Installation Guide</a>`, 
                            true
                        );
                    } else {
                        this.updateHeaderTooltip('icecast-status-text', 'Server Offline');
                    }
                } catch (error) {
                    console.error('Failed to check Icecast status:', error);
                    this.icecastStatus = 'unknown';
                    this.updateHeaderTooltip('icecast-status-text', 'Status Unknown');
                }
            }

            async checkFFmpegStatus() {
                try {
                    const response = await fetch('/api/system/ffmpeg-check');
                    const data = await response.json();
                    
                    if (data.installed) {
                        this.updateHeaderTooltip('ffmpeg-status-text', `FFmpeg ${data.version || 'installed'}`);
                    } else {
                        this.updateHeaderTooltip('ffmpeg-status-text', 
                            `FFmpeg not found. <a href="https://github.com/jerryagenyi/LANStreamer/blob/main/docs/guides/ffmpeg-installation.md" target="_blank" class="text-blue-400 hover:text-blue-300 underline">Installation Guide</a>`, 
                            true
                        );
                    }
                } catch (error) {
                    console.error('Failed to check FFmpeg status:', error);
                    this.updateHeaderTooltip('ffmpeg-status-text', 'Status Unknown');
                }
            }

            updateIcecastUI(data) {
                const serverStatus = document.getElementById('icecast-status-text');
                const serverStatusDot = document.getElementById('icecast-status-dot');
                const listenerCount = document.getElementById('listener-count');
                const listenerBar = document.getElementById('listener-bar');
                const serverHost = document.getElementById('server-host');
                const serverUptime = document.getElementById('server-uptime');

                if (data.running) {
                    if (serverStatus) serverStatus.textContent = 'Online';
                    if (serverStatusDot) serverStatusDot.className = 'status-dot online';
                    
                    if (listenerCount) listenerCount.textContent = `${data.listeners || 42} / 128`;
                    const listenerPercent = Math.min((data.listeners || 42) / 128 * 100, 100);
                    if (listenerBar) listenerBar.style.width = `${listenerPercent}%`;
                    
                    if (data.uptime && serverUptime) {
                        serverUptime.textContent = this.formatUptime(data.uptime);
                    }
                } else if (data.installed === false) {
                    if (serverStatus) serverStatus.textContent = 'Not Found';
                    if (serverStatusDot) serverStatusDot.className = 'status-dot offline';
                    if (listenerCount) listenerCount.textContent = '0 / 128';
                    if (listenerBar) listenerBar.style.width = '0%';
                    if (serverUptime) serverUptime.textContent = '--:--:--';
                } else {
                    if (serverStatus) serverStatus.textContent = 'Connecting...';
                    if (serverStatusDot) serverStatusDot.className = 'status-dot connecting';
                    if (listenerCount) listenerCount.textContent = '0 / 128';
                    if (listenerBar) listenerBar.style.width = '0%';
                    if (serverUptime) serverUptime.textContent = '--:--:--';
                }
            }

            updateHeaderTooltip(elementId, content, isHtml = false) {
                const element = document.getElementById(elementId);
                if (element) {
                    if (isHtml) {
                        element.innerHTML = content;
                    } else {
                        element.textContent = content;
                    }
                }
            }


            formatUptime(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }

            setupEventListeners() {
                // Refresh devices button (in Audio Devices section)
                const refreshBtn = document.querySelector('button[class*="refresh"]');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', async () => {
                        await this.loadAudioDevices(true); // Force refresh
                        this.showNotification('Audio devices refreshed', 'info');
                    });
                }

                // Video Sources collapsible toggle
                const videoToggle = document.getElementById('video-sources-toggle');
                const videoContent = document.getElementById('video-sources-content');
                const videoArrow = document.getElementById('video-sources-arrow');
                
                if (videoToggle && videoContent && videoArrow) {
                    videoToggle.addEventListener('click', () => {
                        const isHidden = videoContent.classList.contains('hidden');
                        if (isHidden) {
                            videoContent.classList.remove('hidden');
                            videoArrow.style.transform = 'rotate(180deg)';
                        } else {
                            videoContent.classList.add('hidden');
                            videoArrow.style.transform = 'rotate(0deg)';
                        }
                    });
                }

                // Lobby Music collapsible toggle
                const musicToggle = document.getElementById('lobby-music-toggle');
                const musicContent = document.getElementById('lobby-music-content');
                const musicArrow = document.getElementById('lobby-music-arrow');
                
                if (musicToggle && musicContent && musicArrow) {
                    musicToggle.addEventListener('click', () => {
                        const isHidden = musicContent.classList.contains('hidden');
                        if (isHidden) {
                            musicContent.classList.remove('hidden');
                            musicArrow.style.transform = 'rotate(180deg)';
                        } else {
                            musicContent.classList.add('hidden');
                            musicArrow.style.transform = 'rotate(0deg)';
                        }
                    });
                }

                // Add stream button
                const addStreamBtn = document.querySelector('button[class*="add"]');
                if (addStreamBtn) {
                    addStreamBtn.addEventListener('click', () => {
                        this.showNotification('Stream configuration modal will be implemented in next phase', 'info');
                    });
                }

                // Note: Volume controls are now handled by the LobbyMusicPlayer component
            }

            startStatusPolling() {
                // Poll every 5 seconds for status updates
                setInterval(async () => {
                    await this.checkIcecastStatus();
                    await this.checkFFmpegStatus();
                }, 5000);
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-20 right-4 z-50 p-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
                
                const colors = {
                    'info': 'bg-blue-500',
                    'success': 'bg-green-500', 
                    'error': 'bg-red-500'
                };
                
                notification.className += ` ${colors[type] || colors.info} text-white`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                // Slide in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);
                
                // Slide out and remove
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            // Development utilities
            getComponentStatus() {
                if (!this.componentManager) {
                    console.warn('ComponentManager not initialized');
                    return null;
                }
                return this.componentManager.getStatusReport();
            }

            refreshComponent(sectionId) {
                if (!this.componentManager) {
                    console.warn('ComponentManager not initialized');
                    return Promise.resolve();
                }
                return this.componentManager.refreshSection(sectionId);
            }

            testStaticFallback() {
                if (!this.componentManager) {
                    this.showNotification('ComponentManager not available', 'error');
                    return;
                }

                // Temporarily disable LobbyMusicPlayer to test static fallback
                const originalComponent = window.LobbyMusicPlayer;
                window.LobbyMusicPlayer = undefined;
                
                this.componentManager.refreshSection('lobby-music-player').then(() => {
                    this.showNotification('Tested static fallback - check console for status', 'info');
                    this.componentManager.logStatus();
                    
                    // Restore component after 5 seconds
                    setTimeout(() => {
                        window.LobbyMusicPlayer = originalComponent;
                        this.componentManager.refreshSection('lobby-music-player');
                        this.showNotification('Restored component', 'success');
                    }, 5000);
                });
            }
        }

        // Initialize dashboard when page loads
        let dashboard;
        document.addEventListener('DOMContentLoaded', () => {
            // Add a small delay to ensure all scripts are loaded
            setTimeout(() => {
                console.log('Initializing LANStreamer Dashboard...');
                dashboard = new LANStreamerDashboard();
            }, 100);
        });
    </script>
    </body>
    </html>